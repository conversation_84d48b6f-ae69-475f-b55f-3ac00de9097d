# **Fusion Document: Foodify Management App**

* **Document Version:** 1.1  
* **Date:** August 2, 2025  
* **Core Purpose:** To provide a unified strategic and tactical guide for the AI-driven development of the Foodify Management App, ensuring all development work is directly aligned with business objectives.

  ## **1\. Vision & Problem Statement**

  ### **1.1. Strategic Requirement (from PRD)**

* **Vision:** To be the indispensable central nervous system for restaurants in Egypt, empowering owners with a simple, powerful, and reliable tool that gives them complete control over their operations, finances, and growth—all from a single app, on any device.  
* **Problem Statement:** Restaurant owners in the Egyptian market face a unique and intense set of challenges. They are squeezed by high commission fees, grapple with chaotic manual operations, and suffer from unreliable infrastructure like internet connectivity. They lack accessible, real-time data to make informed decisions. Foodify directly addresses this by providing a centralized, affordable, and resilient hub to manage the entire restaurant workflow.

  ## **2\. Goals, Success Metrics, & Implementation Prompts**

  ### **2.1. Goal-to-Prompt Traceability Matrix**

| Business Goal | Key Feature(s) | Implementing PRP Prompts |
| ----- | ----- | ----- |
| **A. Improve Operational Speed** | Point of Sale (POS) | 3.2, 3.3, 3.4, 3.5, 3.6 |
| **B. Reduce Operational Errors** | Live Order Hub, Menu Control | 5.1, 5.2, 6.1, 6.2 |
| **C. Increase Profitability** | Analytics & Reporting | 4.1, 4.2 |
| **D. Empower Owner Control** | Dashboard, Offline Mode, App Lifecycle | 1.2, **1.4**, 2.3, 3.5, 4.1, 4.2 |

  ### **2.2. User Personas & Use Cases (from PRD)**

* **Adham (The Owner):** 48, needs to make quick decisions from his phone based on real data, even when he's not in the shop.  
* **Mariam (The Cashier):** 23, needs a system that is lightning-fast, never crashes, and is simple to use under pressure.

  ## **3\. Foundational Setup (PRP Implementation)**

  #### **Goal: Establish a robust, scalable, and correctly configured project foundation.**

**PRP Prompt 1.1: Initialize Flutter Project**

* **AI Task:** "Let's begin by setting up the Foodify project. Your task is to initialize a new Flutter application."  
* **Tech Stack Tags:** `Flutter`, `Dart`, `VS Code`.  
* **Instructions:** Create a new Flutter project named `foodify_app` and clean the `main.dart` file to a simple "Foodify" `Scaffold`.

**PRP Prompt 1.2: Set Up Firebase (Revised)**

* **AI Task:** "Next, we need to connect our Flutter app to our **existing Firebase backend**. This is the most critical integration step."  
* **Instructions:** Use the FlutterFire CLI to configure the app for both Android and iOS, selecting the **same Firebase project** that powers our web admin panel. Add `firebase_core`, `firebase_auth`, and `cloud_firestore`.

**PRP Prompt 1.3: Establish Project Structure & State Management**

* **AI Task:** "To keep our code organized and scalable, let's establish a clean folder structure and set up our state management solution."  
* **Tech Stack Tags:** `Flutter`, `Riverpod`.  
* **Instructions:** Add `flutter_riverpod`, wrap the app in a `ProviderScope`, and create `features`, `common`, `models`, `services`, and `utils` directories.

**PRP Prompt 1.4: Create Splash Screen**

* **AI Task:** "Create a splash screen to provide a professional entry point while the app initializes."  
* **Tech Stack Tags:** `Flutter`.  
* **Instructions:** Create a new `SplashScreen.dart` widget. It should display the Foodify logo and a loading indicator. This screen will be shown immediately on app launch while Firebase initializes and the initial authentication state is determined.

  ## **4\. Authentication Module (PRP Implementation)**

  #### **Goal: Ensure secure and reliable access for restaurant staff, using the same credentials as the web admin panel.**

**PRP Prompt 2.1: Build the Login Screen UI**

* **AI Task:** "Create the user interface for our login screen."  
* **Instructions:** Build a stateless widget with centered `TextField` widgets for email and password, and an `ElevatedButton` for "Sign In".

**PRP Prompt 2.2: Implement Login Logic (Revised)**

* **AI Task:** "Now, let's wire up the login screen to authenticate with Firebase."  
* **Cross-Platform Consideration:** The users of this mobile app are the **exact same users** as the web admin panel. The `AuthRepository` must authenticate against the same shared user pool.  
* **Edge Case Coverage:** Show a loading indicator on the button during the sign-in attempt and display a `SnackBar` with an error message on failure.

**PRP Prompt 2.3: Create Auth State Listener & Router (Revised)**

* **AI Task:** "We need to direct users to the correct screen based on their authentication state."  
* **Instructions:** Create a Riverpod `StreamProvider` listening to `authStateChanges()`. Use this provider to build a wrapper widget that initially shows the `SplashScreen`. Once the provider resolves its first value, it should navigate to either the `LoginScreen` (if no user) or the `DashboardScreen` (if a user is logged in).

## **5\. Point of Sale (POS) Module (PRP Implementation)**

#### **Goal: (A) Improve Operational Speed by creating a fast, intuitive, and offline-capable order-taking system.**

**PRP Prompt 3.1: Define Data Models**

* **AI Task:** "Before building the UI, let's define the data models for our menu and orders."  
* **Instructions:** Create `MenuItem` and `Order` classes in Dart, complete with `fromJson` and `toJson` methods for Firestore compatibility. These models must match the structure used by the web admin panel.

**PRP Prompt 3.2: Build the POS Screen UI**

* **AI Task:** "Let's create the main Point of Sale screen. This is the most important screen for the cashier."  
* **Instructions:** Use a two-pane layout: a `GridView` for the menu on the left, and an Order Cart view on the right with a list of items and a "Charge" button.

**PRP Prompt 3.3: Implement POS State Logic**

* **AI Task:** "Now, let's make the POS screen interactive."  
* **Instructions:** Use a Riverpod `StateNotifierProvider` to manage the state of the current order cart. Tapping a menu item should add it to the cart state, which should instantly update the UI.

**PRP Prompt 3.4: Fetch Live Menu from Firestore**

* **AI Task:** "Let's replace our mock menu data with live data from Firestore."  
* **Edge Case Coverage:** While the data is loading, the Menu Grid area should display a `CircularProgressIndicator`.

**PRP Prompt 3.5: Implement Offline Capability for Menu**

* **AI Task:** "A critical requirement is for the menu to be available even if the internet is down. Let's implement offline caching."  
* **Instructions:** Enable Firestore's offline persistence. The app must serve the menu from the cache when offline.

**PRP Prompt 3.6: Save Completed Order to Firestore**

* **AI Task:** "When the cashier hits the 'Charge' button, we need to save the completed order to the database."  
* **Feedback Loop:** After a successful save, clear the cart and show a confirmation `SnackBar`. On failure (e.g., offline), do not clear the cart and show an error.

## **6\. Dashboard & Menu Management (PRP Implementation)**

#### **Goal: (C) Increase Profitability and (D) Empower Owner Control by providing real-time business insights and simple operational controls.**

**PRP Prompt 4.1 & 4.2: Build and Populate the Dashboard**

* **AI Task:** "Create the Dashboard screen and populate it with live data."  
* **Instructions:** Build a UI with reusable `StatCard` widgets for "Today's Gross Sales" and "Total Orders Today". Use a Riverpod `StreamProvider` to watch today's orders from Firestore and update the stats in real-time.

**PRP Prompt 5.1 & 5.2: Build and Implement Menu Management**

* **AI Task:** "Create the Menu Management screen with a functional 'Sold Out' toggle."  
* **Instructions:** Display all menu items in a `ListView`. Each `ListTile` must have a `Switch` widget bound to the `isAvailable` property of the `MenuItem`. Toggling the switch must update the item's status in Firestore immediately.

## **7\. Live Order Hub (PRP Implementation)**

#### **Goal: (B) Reduce Operational Errors by providing a clear, real-time view of incoming online orders.**

**PRP Prompt 6.1 & 6.2: Build and Populate the Order Hub**

* **AI Task:** "Create the UI for the Live Order Hub and connect it to a live stream of orders."  
* **Instructions:** Build a `TabBarView` with three tabs: "New," "Preparing," and "Ready." Use a Riverpod provider to stream active orders from Firestore and filter them into the correct tabs based on their `status` field. New orders must appear instantly.

