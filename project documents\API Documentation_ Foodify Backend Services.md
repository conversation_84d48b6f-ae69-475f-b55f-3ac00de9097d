# **API Documentation: Foodify Backend Services**

* **Document Version:** 1.0
* **Date:** August 2, 2025
* **Status:** Baseline

## **1\. Introduction & Architecture**

This document specifies the data access API for the Foodify ecosystem. As our backend is built on Google Firebase, this is not a traditional REST or GraphQL API. Instead, this document serves as the official "contract" that all client applications must follow when interacting with the Cloud Firestore database via the Firebase SDKs.

Adherence to these specifications is mandatory for all clients, including the Flutter Management App and the existing Web Admin Panel, to ensure data integrity and consistent system behavior.

## **2\. Authentication**

All data operations require a valid user session, managed by Firebase Authentication.

* **Method:** Email & Password
* **SDK Function:** `signInWithEmailAndPassword(email, password)`
* **User Pool:** A single, shared user pool is used across all Foodify management applications. Credentials for the web admin panel are the same for the mobile/desktop app.
* **Test Credentials (Dev):** Project ID: `mobilify-pro-admin`, user: `<EMAIL>`, password: `password123`.

## **3\. Firestore API Endpoints (Collections)**

The following sections define the primary data collections, their document structures, and the supported SDK operations.

### **3.1. Collection: `menu`**

This collection stores all individual menu items for the restaurant.

* **Path:** `/menu`

#### **Data Model (`MenuItem`)**

{
  "name": "string",
  "description": "string",
  "price": "number",
  "category": "string",
  "imageUrl": "string",
  "isAvailable": "boolean"
}

#### **Supported Operations**

**1\. Read: List All Menu Items**

* **Description:** Fetches all documents from the `menu` collection.

**SDK Example (Dart):**
final firestore = FirebaseFirestore.instance;
final querySnapshot = await firestore.collection('menu').orderBy('name').get();
final menuItems = querySnapshot.docs.map((doc) => MenuItem.fromJson(doc.data())).toList();

*
* **Security:** Requires an authenticated user.

**2\.** Update: **Set Item Availability**

* **Description:** Toggles the `isAvailable` status of a single menu item. This is the primary mechanism for the "Sold Out" feature.

**SDK Example (Dart):**
final firestore \= FirebaseFirestore.instance;
final String itemId \= "DOCUMENT\_ID\_HERE";
await firestore.collection('menu').doc(itemId).update({
  'isAvailable': false // or true
});

*
* **Security:** Requires an authenticated user.

### **3.2. Collection: `orders`**

This collection stores a record of all customer orders, both in-progress and completed.

- Timezone Note: All "today" metrics and date-bound queries should use Africa/Cairo timezone boundaries computed on the client.

* **Path:** `/orders`

#### **Data Model (`Order`)**

{
  "userId": "string", // staff uid who created the order
  "customerId": "string | null", // always null in v1.0
  "customerName": "string", // required; defaults to "Walk-in Customer" for POS
  "status": "string", // one of: "pending", "preparing", "ready", "completed", "rejected"
  "orderType": "string", // one of: "dine_in", "pickup"
  "paymentMethod": "string", // one of: "cash", "card"
  "orderNumber": "number", // sequential per business day
  "notes": "string", // optional
  "createdAt": "timestamp", // server timestamp
  "items": [
    {
      "id": "string",
      "name": "string",
      "quantity": "number",
      "price": "number" // stored as a double
    }
  ]
}


#### **Order Number Generation (Atomic Counter)**

- The `orderNumber` field is assigned using a Firestore transaction against a dedicated counters document, scoped per business day (Africa/Cairo).
- Pattern: read current value, increment, write back atomically; attach the incremented value to the new order within the same transaction to guarantee uniqueness.


- Counters collection shape: `counters/{YYYY-MM-DD}` with `{ currentOrderNumber: number }`, e.g., `counters/2025-08-08` -> `{ currentOrderNumber: 123 }`.


**Pseudo-code (Dart) for Atomic Counter + Order Create**

```dart
// 1) Compute Africa/Cairo business date key (YYYY-MM-DD)
import 'package:timezone/data/latest.dart' as tzdata;
import 'package:timezone/timezone.dart' as tz;
import 'package:intl/intl.dart';

Future<void> createOrderWithAtomicOrderNumber(Map<String, dynamic> orderDraft) async {
  // Initialize tz database once at app start; shown here for completeness
  tzdata.initializeTimeZones();
  final cairo = tz.getLocation('Africa/Cairo');
  final nowCairo = tz.TZDateTime.now(cairo);
  final businessDateKey = DateFormat('yyyy-MM-dd').format(nowCairo); // e.g., 2025-08-08

  final firestore = FirebaseFirestore.instance;
  final countersRef = firestore.collection('counters').doc(businessDateKey);
  final ordersRef = firestore.collection('orders');

  await firestore.runTransaction((tx) async {
    // 2) Read current counter for the day
    final counterSnap = await tx.get(countersRef);
    final current = counterSnap.exists
        ? (counterSnap.data()?['currentOrderNumber'] as int? ?? 0)
        : 0;
    final next = current + 1;

    // 3) Update counter atomically (merge to keep future fields)
    tx.set(countersRef, { 'currentOrderNumber': next }, SetOptions(merge: true));



    // 4) Write new order in the same transaction, attaching orderNumber
    final orderData = <String, dynamic>{
      ...orderDraft, // must include userId, customerId (null), customerName, status, orderType, paymentMethod, items...
      'orderNumber': next,
      'createdAt': FieldValue.serverTimestamp(),
    };

    final newOrderDoc = ordersRef.doc();
    tx.set(newOrderDoc, orderData);
  });
}
```

#### **Supported Operations**


## **5. Status Updates (Order Hub)**

- v1.0 allows flexible status changes from the app (no enforced transition rules). Users can move orders between `pending`, `preparing`, `ready`, `completed`, and `rejected` as needed.

**1\. Create: Save New Order**

* **Description:** Adds a new document to the collection, representing a new customer order.

**SDK** Example **(Dart):**
final firestore \= FirebaseFirestore.instance;
final newOrder \= Order(
  // ... populate order data
  createdAt: FieldValue.serverTimestamp(), // Use server-side timestamp
);
await firestore.collection('orders').add(newOrder.toJson());

*
* **Security:** Requires an authenticated user.

**2\. Read: Stream Active Orders**

* **Description:** Establishes a real-time listener for all orders that are currently active (not yet completed or rejected). This is used to power the Live Order Hub.

**SDK Example (Dart):**
final firestore = FirebaseFirestore.instance;
firestore
    .collection('orders')
    .where('status', whereIn: ['pending', 'preparing', 'ready'])
    .orderBy('createdAt', descending: true)
    .snapshots() // Returns a Stream for real-time updates
    .listen((querySnapshot) {
      // Rebuild UI with the latest list of active orders
    });

*
* **Security:** Requires an authenticated user.

## **4\. Error Handling**

Client applications must gracefully handle exceptions thrown by the Firebase SDK. The following are common error codes:

| Exception Code (`.code`) | Meaning & Common Cause |
| ----- | ----- |
| `invalid-email` | The email address is malformed. |
| `wrong-password` | The password provided is incorrect. |
| `user-not-found` | No account exists for the specified email. |
| `permission-denied` | The authenticated user's permissions (defined in Firestore Security Rules) forbid the requested operation. |
| `unavailable` | The client is offline and cannot connect to the Firestore backend. The operation may complete later if persistence is enabled. |

