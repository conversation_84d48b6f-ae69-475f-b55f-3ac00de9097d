# **Technical Specifications: Foodify Management App**

* **Document Version:** 1.0
* **Date:** August 2, 2025
* **Status:** Baseline

## **1\. Core Purpose**

This document provides the specific technical constraints, standards, and data contracts for the development of the Foodify Management App. It is intended to be the primary technical reference for the development team and AI assistants to ensure consistency, performance, and reliability.

## **2\. Core Technology Stack**

To ensure stability and avoid compatibility issues, development must adhere to the following pinned versions:

* **Framework:** Flutter `3.22.x`
* **Language:** Dart `3.4.x`
* **State Management:** `flutter_riverpod: ^2.5.x`
* **Firebase Integration (FlutterFire):**
  * `firebase_core: ^3.1.x`
  * `firebase_auth: ^5.1.x`
  * `cloud_firestore: ^5.0.x`
* **Development Environment:** Visual Studio Code (latest stable version) with the official Dart and Flutter extensions.


## **2.1. Internationalization**

- The app supports Arabic and English, with Arabic as the default locale.
- An in-app language toggle must be provided.

## **3\. Target Platforms**

The application must be built and tested to ensure full functionality on the following minimum operating system versions:

* **iOS:** `15.0`
* **Android:** `8.0` (API Level 26\)
* **Windows:** Windows 10
* **macOS:** `12.0` (Monterey)

## **4\. System Architecture**

The Foodify app operates on a client-server model with a shared backend, ensuring data consistency across all platforms (mobile, desktop, and the existing web admin panel).

* **Client:** The Flutter application, running natively on target devices. It is responsible for UI, local state management, and offline data caching.
* **Backend:** The existing Firebase project.
  * **Authentication:** Firebase Authentication serves as the single source of truth for all user identities.
  * **Database:** Cloud Firestore is the primary data store for all application data. It is the central hub that all clients (web and Flutter) read from and write to.

graph TD
    subgraph Client Applications
        A\[Flutter App on iOS/Android\]
        B\[Flutter App on Windows/macOS\]
        C{Web Admin Panel (Existing)}
    end

    subgraph Shared Firebase Backend
        D(Firebase Authentication)
        E(Cloud Firestore Database)
    end

    A \-- Reads/Writes \--\> E
    B \-- Reads/Writes \--\> E
    C \-- Reads/Writes \--\> E

    A \-- Authenticates With \--\> D
    B \-- Authenticates With \--\> D
    C \-- Authenticates With \--\> D

    style E fill:\#D5F5E3,stroke:\#333,stroke-width:2px
    style D fill:\#D5F5E3,stroke:\#333,stroke-width:2px

## **5\. Performance & Reliability Requirements**

* **UI Responsiveness:** All UI interactions (button taps, scrolling) must complete and provide feedback in **\< 150ms**.
* **Offline Functionality (Critical):** The Point of Sale (POS) module **must remain 100% functional** for taking new orders and processing cash payments when the device has no internet connection.
* **Data Synchronization:** Upon regaining an internet connection, all locally stored offline transactions must be successfully synced to Firestore within **45 seconds** using the retry schedule: immediately, 10s, 30s, 60s, and 5 minutes.
* **Application Start-up:** Cold start time should be under **4 seconds**. Hot start time should be under **1.5 seconds**.

## **7.1. Time Handling (Africa/Cairo)**
- Bundle a timezone package and tz data to compute day boundaries for Africa/Cairo on-device for "today" queries.

## **7.2. Order Number Generation**
- Use a Firestore transaction against `counters/{YYYY-MM-DD}` (Africa/Cairo date) to atomically increment and assign `orderNumber` per order.

## **7.3. Printing Integration**
- Libraries: `esc_pos_utils_plus` to generate ESC/POS commands; `flutter_bluetooth_printer_plus` to send over Bluetooth.
- Priority: Android first; iOS printing may require MFi-compatible printers and is secondary for v1.0.

## **7.4. Emulator Suite**
- Commit `firebase.json` and emulator configs; run integration tests against the Firebase Emulator Suite locally and in CI.


## **7.5. Settings Configuration**
- Firestore document path: `config/restaurantInfo`.
- Shape: `{ name: string, vatId: string|null, address: string|null, phone: string|null, updatedAt: timestamp }`.
- Local persistence: language selection stored via `shared_preferences`.
- Printer pairing: store locally; attempt auto-reconnect on app start.
- Receipt Header Defaults (until config exists): Name = "Foodify Restaurant"; VAT ID/Address/Phone empty.
- Privacy Policy URL: https://foodify.app/privacy



## **6\. API Contracts & Data Models (Firestore)**

This is the definitive structure for our Firestore collections. All read and write operations from the Foodify app **must** conform to these models to ensure compatibility with the existing web admin panel.

### **6.1. `menu` collection**

Each document represents a single item on the restaurant's menu.

* **Document ID:** Auto-generated by Firestore.

**Fields:**
{
  "name": "string",
  "description": "string",
  "price": "number",
  "category": "string", // e.g., "Appetizers", "Main Courses"
  "imageUrl": "string", // URL to the item's image
  "isAvailable": "boolean" // true \= For Sale, false \= Sold Out
}

*

### **6.2. `orders` collection**

Each document represents a single customer order.

* **Document ID:** Auto-generated by Firestore.

**Fields:**
{
  "userId": "string", // staff uid who created the order
  "customerId": "string | null", // always null for v1.0
  "customerName": "string", // required; defaults to "Walk-in Customer"
  "status": "string", // one of: "pending", "preparing", "ready", "completed", "rejected"
  "orderType": "string", // one of: "dine_in", "pickup"
  "paymentMethod": "string", // one of: "cash", "card"
  "orderNumber": "number", // sequential per business day
  "notes": "string", // optional
  "createdAt": "timestamp", // FieldValue.serverTimestamp()
  "items": [ // array of maps
    {
      "id": "string", // menu item id
      "name": "string",
      "quantity": "number",
      "price": "number" // stored as double at time of order
    }
  ]
}

*

## **7\. Coding Standards & Practices**

* **Null Safety:** All code must be 100% sound null safe.
* **Linter:** The project must use the `flutter_lints` package with its default recommended rules. All code must be free of linter warnings.
* **File Naming:** All Dart files must use `snake_case.dart`.
* **State Management:** All application state that is not local to a single widget must be managed via `Riverpod`. Direct calls to `setState` should be minimized.