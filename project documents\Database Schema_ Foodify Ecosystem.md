# **Database Schema: Foodify Ecosystem**

* **Document Version:** 1.0
* **Date:** August 4, 2025
* **Core Purpose:** To provide a definitive blueprint of the Cloud Firestore database, detailing collections, data relationships, indexing strategies, and validation rules.

## **1\. Schema Overview**

Our database is a NoSQL, document-based Cloud Firestore instance. While it is not a relational database, we enforce data relationships through referenced document IDs. The schema is designed to be simple, scalable, and optimized for the real-time queries required by our applications.

## **2\. Entity-Relationship (ER) Diagram**

This diagram illustrates the logical relationships between our primary data collections.

erDiagram
    USERS {
        string userId (PK)
        string email
        string role
    }

    ORDERS {
        string orderId (PK)
        string userId (FK)
        timestamp createdAt
        string status
        array items
    }

    MENU\_ITEMS {
        string menuItemId (PK)
        string name
        number price
        boolean isAvailable
    }

    USERS ||--o{ ORDERS : "places"
    ORDERS }o--|| MENU\_ITEMS : "contains"

* **Note:** `USERS` represents the data managed by Firebase Authentication, which is implicitly linked to our Firestore collections. The `orders` collection contains a `userId` field, creating a one-to-many relationship. The `items` array within an order contains references to `MENU_ITEMS`.

## **3\. Collections & Data Structures**

### **3.1. `users` (Implicit Collection via Firebase Auth)**

* **Description:** This data is managed by Firebase Authentication and is not a direct Firestore collection we create. However, the `uid` generated here is our primary key for identifying users.
* **Key Fields:**
  * `uid` (string): The unique user ID. This is used as the foreign key in other collections.
  * `email` (string): The user's login email.

### **3.2. `menu` Collection**

* **Description:** Stores all individual menu items.
* **Document ID:** Auto-generated string.
* **Schema:** | Field | Data Type | Description | | :--- | :--- | :--- | | `name` | `string` | The display name of the item. | | `description` | `string` | A short description. | | `price` | `number` | The price of the item. | | `category` | `string` | The category the item belongs to (e.g., "Main Courses"). | | `imageUrl` | `string` | A URL to a photo of the item. | | `isAvailable` | `boolean` | `true` if the item can be sold, `false` if "Sold Out". |

### **3.3. `orders` Collection**

* **Description:** Stores all customer orders.
* **Document ID:** Auto-generated string.
* **Schema:** | Field | Data Type | Description |
| :--- | :--- | :--- |
| `userId` | `string` | The `uid` of the authenticated staff member who created the order. |
| `customerId` | `string | null` | Nullable. Always null in v1.0. Reserved for future consumer app. |
| `customerName` | `string` | Required; defaults to "Walk-in Customer" for POS orders. |
| `status` | `string` | One of: "pending", "preparing", "ready", "completed", "rejected". |
| `orderType` | `string` | One of: "dine_in", "pickup". |
| `paymentMethod` | `string` | One of: "cash", "card". |
| `orderNumber` | `number` | Human-friendly sequential number for the business day. |
| `totalPrice` | `number` | Total amount for the order (double), computed by summing rounded line totals. |
| `notes` | `string` | Optional notes for the order. |
| `createdAt` | `timestamp` | Server-generated timestamp for order creation. |
| `items` | `array<map>` | Each map contains: `id` (string), `name` (string), `quantity` (number), `price` (number as double), `lineTotal` (number as double). |

- Total calculation rule: round each line (quantity × price) to two decimals and store as `lineTotal`, then sum all `lineTotal` values for `totalPrice`.

## **4\. Indexing Strategy**

To ensure our queries are fast and cost-effective, we will leverage Firestore's indexing capabilities.

* **Default Indexes:** Firestore automatically creates single-field indexes for all fields in a document. This supports most of our simple queries (e.g., fetching a menu item by its ID).
* **Composite Indexes:** For more complex queries that filter on multiple fields, we must create composite indexes. The following indexes will be created via the Firebase Console:
  1. **Collection:** `orders`
     * **Fields:** `status` (Ascending), `createdAt` (Descending)
     * **Purpose:** To efficiently query for all *active* orders (`status` is "pending", "preparing", or "ready") and sort them by the newest first. This is critical for the performance of the **Live Order Hub**.
  2. **Collection:** `orders`
     * **Fields:** `userId` (Ascending), `createdAt` (Descending)
     * **Purpose:** To allow a specific user to query for their own order history, sorted by the newest first. This will be used in the future consumer app.


## **6. Order Number Strategy (Atomic Counter)**

- A dedicated `counters` collection stores a document per business date (Africa/Cairo), e.g., `counters/{YYYY-MM-DD}`.
- Clients obtain the next `orderNumber` via a Firestore transaction that reads the current counter, increments it, and uses the incremented value when writing a new order.
- This guarantees uniqueness and avoids collisions across devices.

- Document shape example: `counters/2025-08-08` -> `{ currentOrderNumber: 123 }`.



## **7. Restaurant Settings Document**

- Path: `config/restaurantInfo`
- Shape: `{ name: string, vatId: string|null, address: string|null, phone: string|null, updatedAt: timestamp }`
- Defaults (if not set): Name = "Foodify Restaurant"; VAT ID/Address/Phone = empty.


- Enum validations (to be reflected in Firestore Rules):
  - `status` in ["pending","preparing","ready","completed","rejected"].
  - `orderType` in ["dine_in","pickup"].
  - `paymentMethod` in ["cash","card"].

## **5\. Data Validation Rules**

Data integrity will be enforced at the database level using **Firestore Security Rules**. These server-side rules prevent malformed data from being written, regardless of which client application is used.

**Example `firestore.rules` snippet:**
match /menu/{itemId} {
  allow read: if request.auth != null;
  allow write: if request.auth != null &&
                  request.resource.data.name is string &&
                  request.resource.data.category is string &&
                  request.resource.data.price is number &&
                  request.resource.data.price >= 0 &&
                  (request.resource.data.imageUrl == null || request.resource.data.imageUrl is string) &&
                  request.resource.data.isAvailable is bool;
}

match /orders/{orderId} {
  allow read, write: if request.auth != null &&
    request.resource.data.userId is string &&
    (request.resource.data.customerId == null || request.resource.data.customerId is string) &&
    request.resource.data.customerName is string &&
    request.resource.data.status in ['pending','preparing','ready','completed','rejected'] &&
    request.resource.data.orderType in ['dine_in','pickup'] &&
    request.resource.data.paymentMethod in ['cash','card'] &&
    request.resource.data.orderNumber is number &&
    (request.resource.data.notes == null || request.resource.data.notes is string) &&
    request.resource.data.createdAt is timestamp &&
    request.resource.data.items is list &&
    request.resource.data.items.size() > 0 &&
    request.resource.data.items.every(item,
      item.id is string &&
      item.name is string &&
      item.quantity is number && item.quantity > 0 &&
      item.price is number && item.price >= 0 &&
      item.lineTotal is number && item.lineTotal >= 0
    );
}

*

## **6\. Migration History**

* **Version 1.0 (Initial Schema)**
  * **Date:** August 4, 2025
  * **Action:** Initial creation of the `menu` and `orders` collections.
  * **Details:** The schema was established as defined in this document to support the initial launch of the Web Admin Panel and the Foodify Flutter App. No prior data exists.

