# **Deployment & Maintenance Plan: Foodify App**

* **Document Version:** 1.0  
* **Date:** August 4, 2025  
* **Core Purpose:** To define the automated processes, infrastructure, and procedures for reliably deploying, monitoring, and maintaining the Foodify application and its backend services.

## **1\. Deployment Infrastructure & CI/CD Pipeline**

Our deployment process will be automated using GitHub Actions to ensure consistency and reduce manual error. This pipeline will handle testing, building, and distributing the application.

### **1.1. Infrastructure Diagram**

This diagram illustrates the flow from code commit to live deployment.

graph TD  
    A\[Developer commits to GitHub\] \--\> B{GitHub Actions CI/CD};  
    B \-- On push to \`main\` \--\> C\[Run Tests\];  
    C \-- Success \--\> D\[Build Flutter App \<br\> (.aab/.ipa)\];  
    D \--\> E\[Deploy to Firebase App Distribution \<br\> (for Internal Testing)\];  
    B \-- On creating a \`v\*.\*.\*\` tag \--\> F\[Run Tests\];  
    F \-- Success \--\> G\[Build Flutter App \<br\> (.aab/.ipa)\];  
    G \--\> H\[Deploy to App Stores \<br\> (Google Play & Apple App Store)\];

    subgraph "Firebase Backend"  
        I\[Firestore Rules & Cloud Functions\]  
    end

    A \-- Manual Update \--\> I;

    style B fill:\#D6EAF8,stroke:\#333,stroke-width:2px

### **1.2. CI/CD Pipeline: GitHub Actions**

A workflow file (`.github/workflows/deploy.yml`) will be created to manage the following jobs:

* **Trigger:**
  1. **For Internal Testing:** On every push to the `main` branch.
  2. **For Production Release:** On creating a new tag with the format `v*.*.*` (e.g., `v1.0.1`).
* **Pipeline Steps:**
  1. **Checkout Code:** Clones the repository.
  2. **Setup Flutter:** Installs the specific Flutter version defined in our Technical Specifications.
  3. **Run Tests:** Executes all unit and widget tests (`flutter test`). The build fails if any test fails.
  4. **Integration Tests:** Spin up the Firebase Emulator Suite and run integration tests against it.
  5. **Build App:**
     * Builds a signed Android App Bundle (`.aab`).
     * Builds a signed iOS Archive (`.ipa`).
  6. **Deploy:**
     * **Testing:** Automatically uploads the build artifacts to **Firebase App Distribution** and notifies the internal QA team (placeholder tester list).
     * **Production:** Automatically uploads the build artifacts to the **Google** Play Console and **Apple App Store Connect** for review and release.

## **2\. Monitoring & Alerting**

Proactive monitoring is essential to detect issues before they impact a significant number of users.

* **Application Crash & Performance Monitoring:**  
  * **Tool:** Firebase Crashlytics.  
  * **Setup:** Integrated into the Flutter app.  
  * **Process:** Crashlytics will automatically capture and group crashes and "non-fatal" errors. It will be configured to send an email alert to the development team for any new, high-impact crash affecting more than 1% of users in a 24-hour period. The splash screen initialization will be a key area to monitor for non-fatal errors.  
* **Backend Health Monitoring:**  
  * **Tool:** UptimeRobot (or a similar service).  
  * **Setup:** A simple HTTP-triggered Cloud Function will be created in our Firebase project that returns a `200 OK` status.  
  * **Process:** UptimeRobot will ping this function every 5 minutes. If it fails to get a `200 OK` response twice in a row, it will send an immediate alert to the development team, indicating a potential Firebase service disruption.

## **3\. Rollback Procedures**

### **3.1. Client Application (Flutter App)**

Direct "rollback" is not possible for mobile apps already installed on user devices. The strategy is to **roll forward** with a fix.

* **Step 1: Halt Phased Rollout (if applicable):** If a release is being deployed via a phased rollout on the Google Play Store, immediately halt the rollout to prevent more users from receiving the faulty version.  
* **Step 2: Prepare Hotfix:** Identify the issue and prepare a hotfix branch in Git.  
* **Step 3: Increment Version:** Increment the build number (and patch version if needed, e.g., from `1.0.0` to `1.0.1`).  
* **Step 4: Deploy Hotfix:** Push the new version through the CI/CD pipeline to the app stores for expedited review.

### **3.2. Backend (Firestore Rules)**

Changes to Firestore security rules are deployed manually via the Firebase CLI or console.

* **Step 1: Version Control:** All `firestore.rules` files MUST be committed to the Git repository.  
* **Step 2: Identify Last Known Good Version:** Use `git log` to find the commit hash of the last stable version of the rules file.  
* **Step 3: Revert & Deploy:** Use `git checkout <commit_hash> -- firestore.rules` to revert the file to the stable version.  
* **Step 4: Deploy Rules:** Deploy the reverted rules file using the Firebase CLI: `firebase deploy --only firestore:rules`.

## **4\. Maintenance & Backups**

### **4.1. Firestore Database Backups**

While Google provides disaster recovery, we are responsible for backups to protect against application-level data corruption (e.g., a bad data migration).

* **Tool:** Firebase's built-in scheduled export feature.  
* **Process:**  
  1. A Google Cloud Storage bucket will be created for backups.  
  2. A scheduled job will be configured in the Google Cloud Console to export the entire Firestore database to this bucket.  
* **Schedule:** **Daily**, during off-peak hours (e.g., 4:00 AM EEST).  
* **Retention Policy:** Backups will be retained for 30 days.

### **4.2. Code & Configuration**

* **Tool:** Git / GitHub.  
* **Process:** The GitHub repository is the single source of truth for all application code, CI/CD workflows, and Firebase rules. All changes must be made via pull requests.

