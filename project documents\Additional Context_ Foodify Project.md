# **Additional Context: Foodify Project**

* **Document Version:** 1.0  
* **Date:** August 4, 2025  
* **Core Purpose:** To provide supplementary documentation covering the project roadmap, security posture, user support plans, and specific guidelines for AI-assisted development.

## **1\. Product Roadmap & Prioritized Backlog**

This roadmap outlines the planned evolution of the Foodify Management App beyond the initial release.

#### **v1.0: Core MVP (Current Focus)**

* **Goal:** Deliver a stable, reliable operational tool.  
* **Features:**  
  * Splash Screen & Authentication  
  * Offline-capable Point of Sale (POS)  
  * Live Online Order Hub  
  * Basic Menu Management ("Sold Out" toggle)  
  * Real-time Dashboard (Sales & Order Count)

#### **v1.5: The Smart Upgrade (Next Release)**

* **Goal:** Empower owners with data-driven insights.  
* **Features:**  
  * **Advanced Analytics:** Visual reports on sales trends (by hour/day), top-selling items, and most profitable items.  
  * **CRM (Lite):** A searchable customer database with order history and contact information.  
  * **Enhanced Menu Management:** Ability to add/edit/delete items and categories directly from the app.

#### **v2.0: The Full Suite (Future)**

* **Goal:** Become an all-in-one restaurant management solution.  
* **Features:**  
  * **Inventory Management (Lite):** Track key ingredients, map them to recipes, and receive low-stock alerts.  
  * **Employee Management:** Basic role management (e.g., Manager vs. Cashier) to restrict access to sensitive screens like Analytics.  
  * **Enhanced Integrations:** WhatsApp integration for daily reports and critical alerts.

## **2\. Security & Compliance**

* **Authentication Flow:** Security is handled by Firebase. The flow is:  
  * User enters credentials on the Login Screen.  
  * The app sends credentials directly to Firebase Auth via the SDK.  
  * Firebase returns a secure ID token upon success.  
  * This token is used to authenticate all subsequent requests to Firestore.  
* **Threat Model:** The primary threat is unauthorized access to the management app. This is mitigated by:  
  * **Firebase Authentication:** Enforces strong password policies.  
  * **Firestore Security Rules:** Server-side rules prevent unauthenticated users from accessing any data.  
* **Compliance:** While the management app itself has minimal personal data, the future consumer app will fall under **Egypt's Personal Data Protection Law (PDPL)**. The current architecture is designed with this in mind, ensuring a clear separation of user data that will aid future compliance efforts.

## **3\. User Documentation & Support Plan**

* **v1.0 Support:** For the initial release, support will be high-touch and direct. Restaurant owners will be provided with a direct contact number (e.g., via WhatsApp) for immediate assistance.  
* **Future (Backlog Item):** A simple, in-app "Help & FAQ" section will be developed. This will provide short, easy-to-understand guides (with screenshots) for common tasks like adding a menu item or handling an offline order.

- Internationalization: The app supports Arabic and English, defaulting to Arabic with an in-app toggle.

## **4\. Vibe Coding Checklist (AI Interaction Guide)**

To ensure the most efficient and accurate results when working with an AI assistant on this project, follow these guidelines:

1. **Reference Documents:** Always refer to our planning documents by name.  
   * *Good Prompt:* "Using the `UI/UX Design Assets` document, build the Dashboard screen."  
   * *Bad Prompt:* "Make me a dashboard."  
2. **Be Atomic:** Assign one clear task per prompt.  
   * *Good Prompt:* "First, build the static UI for the Login Screen. In the next step, we will add the Firebase logic."  
   * *Bad Prompt:* "Build the entire login flow."  
3. **Specify the Target:** Clearly state the technology and pattern to use.  
   * *Good Prompt:* "Create a Riverpod `StreamProvider` to watch the `orders` collection."  
   * *Bad Prompt:* "Get the orders."  
4. **Provide Context:** Remind the AI of the existing ecosystem.  
   * *Good Prompt:* "Remember, the `Order` model in our Flutter app must match the data structure already being used by the web admin panel."

## **5\. Competitive Comparison Table**

This table clarifies Foodify's unique value proposition in the Egyptian market.

| Feature | Traditional POS System | Third-Party Aggregator (e.g., Talabat) | Foodify (Our Solution) |
| ----- | ----- | ----- | ----- |
| **Cost Model** | High upfront hardware/license cost. | High commission per order (20-30%). | **Low, flat monthly subscription fee.** |
| **Offline Mode** | Yes (Hardware-based). | **No.** App is useless without internet. | **Yes. Critical POS functions are 100% offline-capable.** |
| **Data Ownership** | Owner controls their data locally. | Aggregator owns the customer data. | **Owner has full control and ownership of their data.** |
| **Ease of Use** | Can be complex and outdated. | Simple for the consumer, complex for the restaurant. | **Designed to be extremely simple and intuitive.** |
| **Local Support** | Varies. | Often handled through call centers. | **Direct, high-touch support tailored to the local market.** |

