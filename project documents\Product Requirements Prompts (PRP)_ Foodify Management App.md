# **Product Requirements Prompts (PRP): Foodify Management App (Part 1 \- Reviewed)**

* **Document Version:** 1.1
* **Date:** August 2, 2025
* **Core Purpose:** To provide tactical, AI-taskable coding instructions for building the Foodify cross-platform application, ensuring seamless integration with the existing web admin panel.

### **Section 1: Project Setup & Core Architecture**

#### **Prompt 1.1: Initialize Flutter Project**

* **AI Task:** "Let's begin by setting up the Foodify project. Your task is to initialize a new Flutter application."
* **Tech Stack Tags:** `Flutter`, `Dart`, `VS Code`.
* **Instructions:**
  1. Create a new Flutter project named `foodify_app`.
  2. Use the `flutter create` command with standard null safety.
  3. Open the project and ensure the default counter app runs correctly on an emulator or test device.
  4. Clean the default `main.dart` file, leaving a simple `MaterialApp` with a `Scaffold` showing the text "Foodify".
* **Output Spec:** A runnable local development environment with a clean starting point.

#### **Prompt 1.2: Set Up Firebase (Revised)**

* **AI Task:** "Next, we need to connect our Flutter app to our **existing Firebase backend**. This is the most critical integration step."
* **Tech Stack Tags:** `Flutter`, `Firebase`, `FlutterFire CLI`.
* **Instructions:**
  1. Your primary task is to connect this new Flutter app to the **same Firebase project** that powers our existing web admin panel. Do not create a new Firebase project.
  2. Use the FlutterFire CLI to configure the app for both Android and iOS, selecting the existing Firebase project when prompted.
  3. Add the necessary Firebase packages to `pubspec.yaml`: `firebase_core`, `firebase_auth`, and `cloud_firestore`.
  4. Initialize Firebase in `main.dart` before `runApp()` is called.
* **Output Spec:** A Flutter app that successfully connects to our pre-existing Firebase backend on launch without errors.

#### **Prompt 1.3: Establish Project Structure & State Management (Revised)**

* **AI Task:** "To keep our code organized and scalable, let's establish a clean folder structure and set up our state management solution."
* **Tech Stack Tags:** `Flutter`, `Dart`, `Riverpod`.
* **Instructions:**
  1. Add the `flutter_riverpod` package to `pubspec.yaml`.
  2. Wrap the `MaterialApp` in `main.dart` with a `ProviderScope`.
  3. Create the following directories inside the `lib` folder to establish our architecture:
     * `features`: For distinct sections of the app (e.g., auth, pos).
     * `common`: For widgets and utilities shared across features.
     * `models`: For our data structures (like `MenuItem`, `Order`).
     * `services`: For repositories that communicate with Firebase.
     * `utils`: For helper functions and constants.
* **Output Spec:** A well-organized project structure ready for feature development, with Riverpod set up as the state management provider.

### **Section 2: Authentication**

#### **Prompt 2.1: Build the Login Screen UI**

* **AI Task:** "Create the user interface for our login screen."
* **Tech Stack Tags:** `Flutter`, `Dart`, `Material Design`.
* **Instructions:**
  1. Create a new file: `lib/features/auth/screens/login_screen.dart`.
  2. Build a stateless widget that displays a centered column with:
     * A logo placeholder (e.g., `Icon(Icons.restaurant_menu)`).
     * A `TextField` for the email address.
     * A `TextField` for the password (with `obscureText: true`).
     * An `ElevatedButton` for "Sign In".
* **Output Spec:** A visually complete but non-functional login screen.

#### **Prompt 2.2: Implement Login Logic (Revised)**

* **AI Task:** "Now, let's wire up the login screen to authenticate with Firebase."
* **Tech Stack Tags:** `Flutter`, `Riverpod`, `Firebase Auth`.
* **Instructions:**
  * Create an `AuthRepository` in `lib/services/auth_service.dart` that handles the `signInWithEmailAndPassword` call to Firebase.
  * Create a Riverpod `Provider` for this repository.
  * In the `LoginScreen`, convert it to a `ConsumerWidget`. When the "Sign In" button is pressed, call the `signIn` method from the repository.
* **Input Spec:** User's email and password from the text fields.
* **Output Spec:** A successful `UserCredential` on correct login, or a `FirebaseAuthException` on failure.
* **Cross-Platform Consideration:**
  This is a critical point. The users of this mobile app are the **exact same users** as the web admin panel. A user who can log into the web admin must be able to log into this mobile app with the exact same credentials. The `AuthRepository` is authenticating against the same shared user pool.
* **Edge Case Coverage:**
  * Show a loading indicator (`CircularProgressIndicator`) on the button while the sign-in attempt is in progress.
  * If an error occurs, display a `SnackBar` with a user-friendly message (e.g., "Invalid credentials. Please try again.").

#### **Prompt 2.3: Create Auth State Listener & Router**

* **AI Task:** "We need to direct users to the correct screen based on whether they are logged in or not."
* **Tech Stack Tags:** `Flutter`, `Riverpod`, `Firebase Auth`.
* **Instructions:**
  1. Create a new Riverpod `StreamProvider` that listens to Firebase's `authStateChanges()` stream.
  2. In `main.dart`, use this provider to build a wrapper widget.
  3. This widget will act as a router: if the auth state stream has a user, show the `DashboardScreen`. If it's null, show the `LoginScreen`.
* **Output Spec:** The app automatically navigates to the dashboard after a successful login and back to the login screen after a logout.

### **Section 3 : Point of Sale (POS)**

#### **Prompt 3.6: Save Completed Order to Firestore**

* **AI Task:** "When the cashier hits the 'Charge' button, we need to save the completed order to the database."
* **Tech Stack Tags:** `Flutter`, `Riverpod`, `Cloud Firestore`.
* **Instructions:**
  1. In your `services` directory, create an `OrderRepository`. It should have a method `saveOrder` that takes an `Order` object as input.
  2. This method will add a new document to the `orders` collection in Firestore. Ensure the data structure matches our `Order` model, including a `serverTimestamp()` for the `createdAt` field.
  3. Include required fields: `userId` (staff uid), `customerId` (null in v1.0), `customerName` (default to "Walk-in Customer"), `status`, `orderType` ("dine_in" | "pickup"), `paymentMethod` ("cash" | "card"), `orderNumber`, optional `notes`, and `items` with `id`, `name`, `quantity`, `price` (double).
  4. In the `POSScreen`, when the "Charge" button is pressed, create the `Order` object from the current cart state and call the `saveOrder` method.
* **Output Spec:** A new document is successfully created in the `orders` collection in Firestore.
* **Feedback Loop:** After a successful save, the order cart should be cleared, and a `SnackBar` should appear saying "Order \#... created successfully."
* **Edge Case Coverage:** Disable the "Charge" button while the save operation is in progress to prevent duplicate orders. If the save fails (e.g., due to being offline), show an error message and *do not* clear the cart, allowing the cashier to retry.

### **Section 4: Dashboard**

#### **Prompt 4.1: Build the Dashboard Screen UI**

* **AI Task:** "Let's create the main Dashboard screen, which is the first thing an owner sees."
* **Tech Stack Tags:** `Flutter`, `Material Design`.
* **Instructions:**
  1. Create a new screen: `lib/features/dashboard/screens/dashboard_screen.dart`.
  2. The layout should be a `Column` containing several prominent "Stat Card" widgets.
  3. Create a reusable `StatCard.dart` widget that takes a `title` (e.g., "Today's Sales") and a `value` (e.g., "EGP 0.00") as props.
  4. On the `DashboardScreen`, lay out stat cards for "Today's Gross Sales" and "Total Orders Today". Use placeholder values for now.
* **Output Spec:** A clean, professional-looking dashboard screen displaying static data.

#### **Prompt 4.2: Fetch and Display Live Dashboard Data**

* **AI Task:** "Now, let's populate the dashboard with live data from our orders."
* **Tech Stack Tags:** `Flutter`, `Riverpod`, `Cloud Firestore`.
* **Instructions:**
  1. In your `OrderRepository`, create a method that fetches all orders from the `orders` collection where the `createdAt` timestamp is for the current day.

### **Prompt 4.3: Define Dashboard Metrics**

- Add two cards:
  - "Today's Gross Sales" — sum of `totalPrice` for orders with `status == 'completed'` created today (Africa/Cairo).
  - "Active Orders" — live count of orders with status in `['pending','preparing','ready']`.
- Ensure timezone calculations use Africa/Cairo (see Tech Specs Time Handling).

  2. Create a Riverpod `StreamProvider` that uses this method to provide a live stream of today's orders.
  3. On the `DashboardScreen`, watch this provider. As the stream updates, calculate the total sales and the number of orders and pass the results to your `StatCard` widgets.
* **Feedback Loop:** When a new order is created on the POS screen, the numbers on the dashboard should update automatically in real-time.
* **Edge Case Coverage:** The dashboard should correctly handle the case where there are no orders for the day, displaying "EGP 0.00" and "0 Orders".

### **Section 5: Menu Management**

#### **Prompt 5.1: Build the Menu Management Screen UI**

* **AI Task:** "We need a screen where the owner can manage menu item availability."
* **Tech Stack Tags:** `Flutter`, `Material Design`.
* **Instructions:**
  1. Create a new screen: `lib/features/menu/screens/menu_management_screen.dart`.
  2. The screen should display a list of all menu items fetched from Firestore.
  3. Use a `ListView.builder` to display the items. Each item should be represented by a `ListTile`.
  4. The `ListTile` should show the item's name and price. Crucially, it must have a `Switch` widget as its `trailing` property.
* **Output Spec:** A list of menu items, each with a toggle switch.

#### **Prompt 5.2: Implement the "Sold Out" Toggle Logic**

* **AI Task:** "Let's make the 'Sold Out' switches functional. This is a critical feature."
* **Tech Stack Tags:** `Flutter`, `Riverpod`, `Cloud Firestore`.
* **Instructions:**
  1. In your `MenuRepository`, create a method `updateItemAvailability` that takes the `itemId` and a new boolean `isAvailable` status. This method will update the `isAvailable` field for the corresponding document in the `menu` collection.
  2. In the `MenuManagementScreen`, when a user toggles a `Switch`, call this `updateItemAvailability` method.
  3. The `value` of the `Switch` should be bound to the `isAvailable` property of the `MenuItem` object.
* **Feedback Loop:** Toggling the switch should update the item in Firestore. Because the list is powered by a live stream of data from Firestore, the UI should persist the switch's state automatically.
* **Cross-Platform Consideration:** Updating an item's availability here **must** be reflected immediately in the POS screen (where the item should no longer be displayed if `isAvailable` is false) and on the web admin panel we designed previously, as they all read from the same database field.

### **Section 6: Live Online Order Hub**

#### **Prompt 6.1: Build the Order Hub Screen UI**

* **AI Task:** "Create the UI for the Live Order Hub, which will display incoming online orders."
* **Tech Stack Tags:** `Flutter`, `Material Design`.
* **Instructions:**
  1. Create a new screen: `lib/features/orders/screens/order_hub_screen.dart`.
  2. The layout should be a `TabBarView` with three tabs: "New," "Preparing," and "Ready."
  3. Each tab's view will contain a `ListView.builder` to display `OrderCard` widgets.
  4. Create a reusable `OrderCard.dart` widget that displays key order information: customer name, total price, and a list of items.
* **Output Spec:** A tabbed interface for managing orders, populated with mock data for now.


### **Section 7: Settings Screen**

#### **Prompt 7.1: Build the Settings Screen UI**
- Add a screen at `lib/features/settings/screens/settings_screen.dart` with sections:
  - Printer: Pair/select default Bluetooth printer; show connection status; test print button.
  - Receipt Header: Edit Name, VAT ID (optional), Address (optional), Phone (optional). Save to Firestore `config/restaurantInfo`.
  - Language: Toggle Arabic/English; persist locally with `shared_preferences`.
  - Legal: Link to Privacy Policy (https://foodify.app/privacy).

#### **Prompt 7.2: Wire Up Settings Logic**
- Create a `RestaurantInfoRepository` to read/write `config/restaurantInfo`.
- Create providers for the restaurant info and printer state.
- Implement a test print that uses the current header and prints a short sample receipt (Arabic, bitmap rendering).

#### **Prompt 6.2: Fetch and Display Live Orders**

* **AI Task:** "Let's connect the Order Hub to a live stream of online orders from Firestore."
* **Tech Stack Tags:** `Flutter`, `Riverpod`, `Cloud Firestore`.
* **Instructions:**
  1. In your `OrderRepository`, create a method that streams all orders where the `status` is `pending`, `preparing`, or `ready`.
  2. In the `OrderHubScreen`, use a Riverpod provider to watch this stream.
  3. Filter the list of all active orders and pass the correct sub-list to each tab's `ListView` based on its status.
* **Output Spec:** New online orders created (e.g., by the future consumer app or manually in Firestore for testing) appear instantly in the "New" tab without needing a refresh.

