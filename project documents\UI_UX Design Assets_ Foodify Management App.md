# **UI/UX Design Assets: Foodify Management App**

* **Document Version:** 1.1
* **Date:** August 2, 2025
* **Core Purpose:** To provide definitive visual and interactive guidance for the front-end development of the Foodify app, ensuring a consistent, accessible, and user-friendly experience.

## **1\. Design Philosophy & Core Principles**

* **Clarity First:** The interface must be immediately understandable to users who are under pressure and may not be tech-savvy.
* **Calm & Controlled:** The design aims to reduce stress. It achieves this through a clean layout, generous spacing, and a professional, muted color palette.
* **Efficiency is Key:** Every interaction is optimized to be fast and require the minimum number of taps. Touch targets are large and forgiving.

## **2\. High-Level User Flow**

This diagram illustrates the primary journey a user will take through the application, starting from the initial launch.

graph TD
    A\[App Launch\] \--\> SP\[Splash Screen\];
    SP \-- App Initializes \--\> B{User Authenticated?};
    B \-- No \--\> C\[Login Screen\];
    C \-- Enters Credentials \--\> D{Auth Success?};
    D \-- Yes \--\> E\[Dashboard Screen\];
    D \-- No \--\> C;
    B \-- Yes \--\> E;

    E \--\> F\[Navigate to POS Screen\];
    E \--\> G\[Navigate to Menu Management\];

    F \-- Adds Items \--\> F;
    F \-- Taps 'Charge' \--\> H\[Order Saved\];
    H \--\> F;

    G \-- Toggles 'Sold Out' \--\> G;

    subgraph "Core Loop"
        F
        G
    end

## **3\. Component Library & Design System**

This is the foundational set of rules for all UI elements.

### **3.1. Color Palette**

The palette is designed for clarity and works in both Light and Dark modes.

| Use Case | Light Mode HEX | Dark Mode HEX | Notes |
| ----- | ----- | ----- | ----- |
| **Primary** | `#2A7F62` | `#35A57F` | Main action color (buttons, active toggles). |
| **Background** | `#F7F9FC` | `#121212` | Main screen background. |
| **Surface** | `#FFFFFF` | `#1E1E1E` | Card, modal, and panel backgrounds. |
| **Text Primary** | `#1A202C` | `#E2E8F0` | For headlines and main body text. |
| **Text Secondary** | `#718096` | `#A0AEC0` | For descriptions and less important text. |
| **Border** | `#E2E8F0` | `#4A5568` | For input fields and card outlines. |
| **Success** | `#38A169` | `#68D391` | For success messages. |
| **Error** | `#E53E3E` | `#FC8181` | For error messages and alerts. |

### **3.2. Typography**

* **Font Family:** **Inter**. It's chosen for its excellent readability on screens and its clean, professional look. It also has great support for both English and Arabic. The default app locale is Arabic with an in-app language toggle for English.
* **Type Scale:**
  * **Display:** 48px, Bold (For large numbers on Dashboard cards)
  * **Headline 1:** 32px, Bold (Screen titles)
  * **Headline 2:** 24px, Semi-Bold (Section titles)
  * **Body Large:** 18px, Regular (Main text, list items)
  * **Body Small:** 16px, Regular (Secondary text, descriptions)
  * **Button Text:** 16px, Medium
  * **Caption:** 14px, Regular (Helper text)

### **3.3. Spacing & Layout**

* **Base Unit:** 8px
* **Scale:** `spacing-1` (4px), `spacing-2` (8px), `spacing-3` (16px), `spacing-4` (24px), `spacing-5` (32px), `spacing-6` (48px).
* **Standard Padding:** All screen content should have a minimum horizontal padding of `spacing-3` (16px).
* **Component Spacing:** Use `spacing-3` (16px) between most vertical elements.

### **3.4. Component Specifications**

* **Buttons (Primary):**
  * **Default:** Solid `Primary` background color, `Button Text` color, `spacing-3` vertical & `spacing-4` horizontal padding, 12px border-radius.
  * **Disabled:** Light gray background, disabled text color.
* **Input Fields:**
  * **Default:** `Border` color outline (1px), `Background` color fill. `spacing-3` padding inside. 12px border-radius.
  * **Focused:** `Primary` color outline (2px).
* **Toggle Switch:**
  * **On:** `Primary` color background.
  * **Off:** `Border` color background.
* **Cards:**
  * **Background:** `Surface` color.
  * **Border Radius:** 16px.
  * **Shadow:** A subtle shadow to create elevation (e.g., `BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 10, offset: Offset(0, 4))`).

## **4\. Wireframes & Interaction Notes**

### **4.1. Splash Screen**

* **Functional Requirement:** Provide a professional entry point while the app initializes Firebase and checks auth state.
* **Low-Fidelity Wireframe:**

\+--------------------------------------+
|                                      |
|                                      |
|                                      |
|          \[ Foodify Logo \]            |
|                                      |
|           ( Foodify )                |
|                                      |
|                                      |
|                                      |
|      \[ Loading Indicator ... \]       |
|                                      |
\+--------------------------------------+

* **Interaction Notes:**
  * This screen is displayed immediately upon app launch.
  * It remains visible while the app connects to Firebase and the `authStateChanges()` stream provides its first value.
  * Once the initial auth state is determined, the app automatically navigates to either the **Login Screen** or the **Dashboard Screen**. There is no user interaction on this screen.

### **4.2. Point of Sale (POS) Screen (Tablet View)**

* **Functional Requirement:** Improve Operational Speed.
* **Low-Fidelity Wireframe:**

\+------------------------------------+-------------------------+
|                                    |                         |
|  \[Tabs: Main, Appetizers, Drinks\]  |   Order \#123            |
|                                    |                         |
|  \+--------+ \+--------+ \+--------+  |   \- Shawarma   EGP 75   |
|  | Item 1 | | Item 2 | | Item 3 |  |   \- Pepsi      EGP 15   |
|  \+--------+ \+--------+ \+--------+  |                         |
|                                    |                         |
|  \+--------+ \+--------+ \+--------+  |   \-------------------   |
|  | Item 4 | | Item 5 | | Item 6 |  |   Subtotal:    EGP 90   |
|  \+--------+ \+--------+ \+--------+  |   Total:       EGP 90   |
|                                    |                         |
|                                    |                         |
|                                    |   \+-------------------+ |
|                                    |   |      CHARGE       | |
|                                    |   \+-------------------+ |
\+------------------------------------+-------------------------+

* **Interaction Notes:**
  * Tapping an **Item card** on the left instantly adds it to the **Order Cart** on the right. This triggers the `addToCart` method in the `StateNotifier`.
  * Tapping the **CHARGE button** triggers the `saveOrder` function and opens a payment modal. On success, it clears the cart.

### **4.3. Menu Management Screen (Mobile View)**

### **4.4. Receipts & Kitchen Tickets (Android/iOS, Bluetooth ESC/POS)**

- Protocol: Bluetooth ESC/POS printers prioritized for v1.0.
- Platforms: Android (priority 1), iOS (priority 2). Desktop printing out of scope.
- Receipt Header: Restaurant Name (required), VAT ID if applicable, Logo/Address/Phone recommended.
- Body: Order Number, Date/Time (Africa/Cairo), Cashier Name (from userId), Line Items with qty, name, price and line total, Subtotal, Total, Payment Method.
- Kitchen Ticket: Same order details but exclude all prices.
- Rendering: Arabic receipts may be rendered as bitmaps for reliable shaping across low-cost ESC/POS printers.
- Test Print (Settings): Print Arabic-only sample: "TEST PRINT — FOODIFY", current date/time (Africa/Cairo), and 1–2 sample lines.



* **Functional Requirement:** Reduce Operational Errors.
* **Low-Fidelity Wireframe:**

\+--------------------------------------+
|  \< Back   Menu Management            |
\+--------------------------------------+
|                                      |
|  \+-------------------------------+   |
|  | Shawarma Lahma      \[Switch ON\] |   |
|  | EGP 75.50                     |   |
|  \+-------------------------------+   |
|                                      |
|  \+-------------------------------+   |
|  | Fries               \[Switch ON\] |   |
|  | EGP 30.00                     |   |
|  \+-------------------------------+   |
|                                      |
|  \+-------------------------------+   |
|  | Koshary             \[Switch OFF\]|   |
|  | EGP 45.00                     |   |
|  \+-------------------------------+   |
|                                      |
\+--------------------------------------+

* **Interaction Notes:**
  * Toggling a **Switch** immediately triggers the `updateItemAvailability` function, passing the item's ID and the new boolean state.
  * The UI should show a subtle loading indicator on the switch itself while the update is in progress to provide feedback to the user.

