# **Existing Code & Architecture: Foodify Ecosystem**

* **Document Version:** 1.0  
* **Date:** August 2, 2025  
* **Core Purpose:** To provide a comprehensive overview of the existing technical landscape for the Foodify project. This document serves as the primary context for any AI assistant or developer tasked with building the new Flutter management app, ensuring seamless integration and adherence to established patterns.

## **1\. System Overview**

The Foodify ecosystem consists of a shared backend on Google Firebase and, currently, one client application: a web-based admin panel. The new Flutter application will be the **second client** to this existing backend, sharing the same database and authentication system.

graph TD  
    subgraph Shared Firebase Backend  
        B(Firebase Authentication)  
        C(Cloud Firestore Database)  
    end

    subgraph Existing Client  
        A\[Web Admin Panel\]  
    end

    subgraph New Client (To Be Built)  
        D\[Flutter App \- iOS/Android/Desktop\]  
    end

    A \-- Reads/Writes Data \--\> C  
    D \-- Reads/Writes Data \--\> C

    A \-- Authenticates With \--\> B  
    D \-- Authenticates With \--\> B

    style C fill:\#D5F5E3,stroke:\#333,stroke-width:2px

## **2\. Backend Architecture: Google Firebase**

Our backend is fully serverless and leverages the following Firebase services.

* **Firebase Authentication:**  
  * **Purpose:** Manages all user identity and access control.  
  * **Method:** Email/Password authentication.  
  * **Key Consideration:** This is a **single user pool**. Any user who can log in to the web admin panel must be able to log in to the new Flutter app with the same credentials.  
* **Cloud Firestore:**  
  * **Purpose:** Serves as the primary real-time database for all application data.  
  * **Data Model:** NoSQL, document-based. The structure is flexible but strictly governed by the data models defined below.

## **3\. Core Data Schema (Firestore Collections)**

This is the definitive data contract. All clients **must** read and write data conforming to these structures.

#### **3.1. `menu` Collection**

* **Purpose:** Stores all menu items.

**Structure Snippet (Single Document):**  
{  
  "name": "Shawarma Lahma",  
  "description": "Premium beef shawarma with tahini sauce.",  
  "price": 75.50,  
  "category": "Main Courses",  
  "imageUrl": "\[https://example.com/images/shawarma.jpg\](https://example.com/images/shawarma.jpg)",  
  "isAvailable": true  
}

* 

#### **3.2. `orders` Collection**

* **Purpose:** Stores all customer orders.

**Structure Snippet (Single Document):**
{
  "userId": "staff_uid_123",
  "customerId": null,
  "customerName": "Walk-in Customer",
  "status": "pending",
  "orderType": "dine_in",
  "paymentMethod": "cash",
  "orderNumber": 1,
  "notes": "No onions",
  "createdAt": "August 2, 2025 at 9:30:15 AM UTC+2", // Firestore Timestamp
  "items": [
    { "id": "menu_item_id_1", "name": "Shawarma Lahma", "quantity": 2, "price": 75.50 },
    { "id": "menu_item_id_2", "name": "Coca-Cola", "quantity": 1, "price": 14.00 }
  ]
}

* 

## **4\. Existing Client: Web Admin Panel**

* **Technology:** React, Vite, Tailwind CSS.  
* **Purpose:** Serves as the initial management interface. Its functionality sets the precedent for the features required in the Flutter app.  
* **Key Implemented Features:**  
  * User login/logout against the shared Firebase Auth pool.  
  * A real-time dashboard displaying sales.  
  * A view of the `menu` collection with the ability to edit items.  
  * A Kanban board that displays active orders from the `orders` collection.

## **5\. Bootstrap for New Flutter App**

The new Foodify Flutter app should be initialized with the following dependencies and structure.

#### **5.1. Directory-Purpose Mapping**

This is the prescribed folder structure within the `lib` directory:

* `/models`: Will contain the Dart class definitions for `MenuItem` and `Order`, mirroring the JSON structures above.  
* `/services`: Will contain the repository classes (`AuthRepository`, `MenuRepository`, `OrderRepository`) responsible for all communication with the Firebase SDK.  
* `/features`: Will contain the UI and feature-specific logic, broken down into sub-directories like `/pos`, `/dashboard`, etc.  
* `/common`: For shared widgets, constants, and utilities.

#### **5.2. Initial `pubspec.yaml` Dependencies**

This is the foundational dependency list. The AI assistant should start with these versions to ensure stability.

\# pubspec.yaml

dependencies:  
  flutter:  
    sdk: flutter

  \# Firebase  
  firebase\_core: ^3.1.1  
  firebase\_auth: ^5.1.1  
  cloud\_firestore: ^5.0.2

  \# State Management  
  flutter\_riverpod: ^2.5.1

  \# Add other packages as needed

dev\_dependencies:  
  flutter\_test:  
    sdk: flutter  
  flutter\_lints: ^4.0.0

#### **5.3. Setup Instructions**

1. Initialize a new Flutter project.  
2. Use the `flutterfire configure` command from the FlutterFire CLI.  
3. **Crucially, select the existing Firebase project** when prompted to ensure the app connects to the correct backend.  
4. Add the dependencies listed in section 5.2 to `pubspec.yaml`.  
5. Establish the directory structure outlined in section 5.1.

