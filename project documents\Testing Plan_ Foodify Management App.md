\# Testing Plan: Foodify Management App

\* \*\*Document Version:\*\* 1.1  
\* \*\*Date:\*\* August 2, 2025  
\* \*\*Status:\*\* Baseline

\#\# 1\. Introduction

This document outlines the testing strategy for the Foodify Management App (v1.0). The purpose of this plan is to validate the application's functionality, reliability, performance, and integration with the existing backend and web admin panel. It will serve as a guide for both automated and manual testing efforts to ensure a high-quality, production-ready release.

\---

\#\# 2\. Testing Scope

\#\#\# 2.1. In Scope

The following features of the MVP are in scope for this testing phase:  
\* App Lifecycle, including the splash screen and startup navigation.  
\* User Authentication (Login/Logout).  
\* Point of Sale (POS) functionality, including order creation and payment processing.  
\* \*\*Critical:\*\* Offline capabilities of the POS module.  
\* Dashboard with live sales and order count metrics.  
\* Menu Management, specifically the "Sold Out" toggle functionality.  
\* Real-time data synchronization with the Cloud Firestore backend.  
\* Data integrity and consistency with the existing Web Admin Panel.

\#\#\# 2.2. Out of Scope

The following features are not part of the v1.0 release and are therefore out of scope for this testing plan:  
\* Advanced inventory management.  
\* Employee management and role-based access control.  
\* Advanced CRM and loyalty features.  
\* Table reservation system.  
\* Integration with third-party delivery services.

\---

\#\# 3\. Testing Frameworks & Tools

\* \*\*Unit & Widget Testing:\*\* \`flutter\_test\` (included with the Flutter SDK).  
\* \*\*Integration & End-to-End (E2E) Testing:\*\* \`integration\_test\` (included with the Flutter SDK).  
\* \*\*Backend Validation:\*\* Manual verification using the Firebase Console and the existing Web Admin Panel.

\---

\#\# 4\. Test Cases

\#\#\# 4.1. App Lifecycle & Navigation

| Test ID | Priority | Description                                          | Steps                                                                                                                               | Expected Outcome                                                                                                                            |  
| :------ | :------- | :--------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------ |  
| \*\*LC-01\*\* | High     | Verify splash screen behavior on fresh app start. | 1\. Ensure the app is fully terminated. \<br\> 2\. Launch the app from the home screen. \<br\> 3\. Observe the initial screen. | The splash screen is displayed immediately. After a short delay for initialization, the user is correctly navigated to the Login Screen. |  
| \*\*LC-02\*\* | High     | Verify splash screen behavior for a logged-in user. | 1\. Log into the app. \<br\> 2\. Fully terminate the app. \<br\> 3\. Relaunch the app.                                                    | The splash screen is displayed immediately. After initialization, the user is correctly navigated directly to the Dashboard Screen.     |

\#\#\# 4.2. Authentication

| Test ID | Priority | Description                                        | Steps                                                                                                                 | Expected Outcome                                                                                                     |  
| :------ | :------- | :------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------- |  
| \*\*AUTH-01\*\* | High     | Successful user login.                             | 1\. Open the app. \<br\> 2\. Enter valid credentials. \<br\> 3\. Tap "Sign In".                                              | User is navigated to the Dashboard screen.                                                                           |  
| \*\*AUTH-02\*\* | High     | Failed login (wrong password).                     | 1\. Open the app. \<br\> 2\. Enter a valid email and an incorrect password. \<br\> 3\. Tap "Sign In".                        | A \`SnackBar\` or error message appears stating "Invalid credentials". User remains on the login screen.                 |  
| \*\*AUTH-03\*\* | High     | \*\*Integration:\*\* Login with Web Admin credentials. | 1\. Ensure a user exists in Firebase Auth. \<br\> 2\. Use that user's credentials to log into the Flutter app. | The user successfully logs in. This validates the shared user pool.                                                     |

\#\#\# 4.3. Point of Sale (POS)

| Test ID | Priority | Description                                | Steps                                                                                                                                                           | Expected Outcome                                                                                                                                                           |  
| :------ | :------- | :----------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |  
| \*\*POS-01\*\* | High     | Add items to cart and verify total.        | 1\. Navigate to the POS screen. \<br\> 2\. Tap on multiple menu items.                                                                                              | Each tapped item appears in the order cart. The total price updates correctly with each addition.                                                                          |  
| \*\*POS-02\*\* | High     | Create and save a new order (Online).      | 1\. Add items to the cart. \<br\> 2\. Tap the "Charge" button. \<br\> 3\. Complete the payment flow.                                                                   | A success message is shown. The cart is cleared. A new document appears in the \`orders\` collection in Firestore.                                                         |  
| \*\*POS-03\*\* | Critical | \*\*Offline:\*\* Take an order while offline.  | 1\. \*\*Disable device internet (WiFi & Mobile Data).\*\* \<br\> 2\. Open the app and navigate to the POS. \<br\> 3\. Add items to the cart. \<br\> 4\. Tap "Charge" and process as a cash sale. | The app remains fully responsive. The order is processed successfully from the user's perspective. The cart is cleared.                                                    |  
| \*\*POS-04\*\* | Critical | \*\*Offline:\*\* Sync offline order.           | 1\. Perform steps for \*\*POS-03\*\*. \<br\> 2\. \*\*Re-enable device internet.\*\* \<br\> 3\. Wait 60 seconds. \<br\> 4\. Check the \`orders\` collection in Firestore and the Web Admin Panel. | The order created while offline now appears in Firestore and is visible on the Web Admin Panel.                                                                            |

\#\#\# 4.4. Menu Management

| Test ID | Priority | Description                                        | Steps                                                                                                                                                           | Expected Outcome                                                                                                                                                           |  
| :------ | :------- | :------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |  
| \*\*MENU-01\*\* | High     | Toggle item as "Sold Out".                         | 1\. Navigate to the Menu Management screen. \<br\> 2\. Toggle the switch for a specific item to "Off".                                                              | The switch state is saved. The UI reflects the "Off" state. The \`isAvailable\` field for that item in Firestore is updated to \`false\`.                                       |  
| \*\*MENU-02\*\* | Critical | \*\*Integration:\*\* Verify "Sold Out" item is hidden. | 1\. Perform steps for \*\*MENU-01\*\*. \<br\> 2\. Navigate to the POS screen in the Flutter app. \<br\> 3\. Refresh the menu page on the \*\*Web Admin Panel\*\*. | The "Sold Out" item is no longer visible or selectable on the POS screen. The Web Admin Panel also reflects the item as unavailable.                                        |

\#\#\# 4.5. Dashboard

| Test ID | Priority | Description                   | Steps                                                                                             | Expected Outcome                                                                                                                                     |  
| :------ | :------- | :---------------------------- | :------------------------------------------------------------------------------------------------ | :--------------------------------------------------------------------------------------------------------------------------------------------------- |  
| **DASH-01** | High     | Real-time dashboard update (Africa/Cairo). | 1. Set device timezone to a non-Cairo timezone. <br> 2. Have the Dashboard screen open and visible. <br> 3. Create a new order. | The "Today's Gross Sales" and "Total Orders Today" correctly compute "today" using Africa/Cairo timezone and update in real-time without a manual refresh. |

\---

\#\# 5\. Performance Benchmarks

The application will be tested against the performance targets defined in the Technical Specifications.

\* \*\*UI Responsiveness:\*\* \< 150ms per interaction.  
\* \*\*Offline Sync Time:\*\* \< 45 seconds to sync after regaining connectivity.  
\* \*\*Cold Start Time:\*\* \< 4 seconds.  
\* \*\*Hot Start Time:\*\* \< 1.5 seconds.

\---

\#\# 6\. Executable Instructions

Tests will be executed from the command line in the root directory of the Flutter project.

\* \*\*To run all unit and widget tests:\*\*  
    \`\`\`bash  
    flutter test  
    \`\`\`  
\* \*\*To run all integration tests:\*\*  
    \`\`\`bash  
    flutter test integration\_test  
    \`\`\`