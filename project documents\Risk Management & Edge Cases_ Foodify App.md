# **Risk Management & Edge Cases: Foodify App**

* **Document Version:** 1.0  
* **Date:** August 2, 2025  
* **Core Purpose:** To proactively identify, assess, and define mitigation strategies for potential failures and edge cases within the Foodify ecosystem, ensuring the application is robust, reliable, and trustworthy.

## **1\. Risk Assessment Matrix**

This matrix prioritizes potential risks based on their likelihood and business impact, allowing us to focus our efforts on the most critical issues.

| Risk ID | Risk Description | Likelihood | Impact | Priority |
| ----- | ----- | ----- | ----- | ----- |
| **RISK-01** | **Offline Data Sync Failure:** An order taken offline fails to upload to Firestore upon reconnection. | Medium | **Critical** | **Highest** |
| **RISK-02** | **Concurrent Data Modification:** Web admin and Flutter app modify the same item simultaneously. | Low | High | High |
| **RISK-03** | **Authentication State Desync:** User's session becomes invalid (e.g., password changed elsewhere). | Medium | High | High |
| **RISK-04** | **Incomplete Data on Read:** A document read from Firestore is missing a required field. | Medium | Medium | Medium |
| **RISK-05** | **Splash Screen Hang:** App gets stuck on the splash screen due to initialization failure. | Low | High | Medium |

## **2\. Top 5 Error Patterns & Mitigation Strategies**

### **2.1. RISK-01: Offline Data Sync Failure**

* **Pattern:** A cashier processes several orders on the POS while the device is offline. When internet connectivity is restored, a network blip or server error causes one or more of these orders to fail to sync with Firestore.  
* **Impact:** **Critical.** This results in lost revenue and incorrect end-of-day reports. The restaurant has fulfilled an order that officially "doesn't exist" in the system.  
* **Mitigation Strategy:**  
  1. **Local Queue:** All orders created offline will be saved to a persistent local queue on the device (e.g., using a simple local database like `sembast` or even a JSON file). An order is only removed from this queue upon successful confirmation of its upload to Firestore.  
  2. **Background Sync Service:** A background process will periodically attempt to sync any orders remaining in the local queue. This service will use an exponential backoff strategy with attempts at: immediately, 10s, 30s, 60s, and 5 minutes.
  3. **User Notification:** If an order still fails after the above retries, the app's UI must display a persistent, non-dismissible alert (e.g., a red badge on a "Sync Status" icon) and allow manual retry from a dedicated screen.
  4. **Manual Retry:** Tapping this alert will navigate the user to a "Sync Issues" screen, showing a list of unsynced orders and providing a "Retry All" button for manual intervention.  
* **Test Case:** `POS-04` in the Testing Plan is the primary validation for this.

### **2.2. RISK-02: Concurrent Data Modification**

* **Pattern:** The owner on the web admin panel changes the price of "Shawarma" from 75 to 80 EGP. At the exact same moment, a manager using the Flutter app changes the same item's price to 78 EGP.  
* **Impact:** High. This leads to inconsistent pricing, customer confusion, and incorrect sales data.  
* **Mitigation Strategy:**  
  1. **Last Write Wins:** Firestore's default behavior is "last write wins." We will formally adopt this as our conflict resolution strategy. The last submitted change will overwrite any previous ones.  
  2. **Real-time UI Updates:** Since all clients are subscribed to real-time listeners, the client whose write "lost" will see their UI automatically update with the winning value moments later. This provides immediate, albeit passive, feedback.  
  3. **Use Atomic Transactions for Critical Updates:** For more complex future updates (like deducting from a shared inventory count), we will use Firestore Transactions to ensure the operation either completes fully or not at all, preventing partial data updates.  
* **Test Case:** A manual test case where two users modify the same menu item from the web admin and Flutter app simultaneously.

### **2.3. RISK-03: Authentication State Desynchronization**

* **Pattern:** A user is logged into the Flutter app. They then use the web admin panel to change their password. The session on the Flutter app is now technically invalid but may not be immediately terminated.  
* **Impact:** High. A user could potentially perform actions with an outdated session, which could be a security risk.  
* **Mitigation Strategy:**  
  1. **ID Token Listener:** The app will actively listen for changes to the user's ID token. Firebase automatically revokes ID tokens when a user's password is changed or their account is disabled.  
  2. **Force Re-authentication:** Upon detecting an invalid or revoked token, the app will immediately clear all local user data and force-navigate the user back to the **Login Screen** with a message: "Your session has expired. Please log in again."  
* **Test Case:** Log in on the Flutter app. Change the user's password via the Firebase Console or web admin. Observe that the Flutter app automatically logs the user out.

### **2.4. RISK-04: Incomplete Data on Read**

* **Pattern:** The app tries to read an `Order` document from Firestore, but due to a past bug or manual data entry error, the `status` field is missing.  
* **Impact:** Medium. This could cause the app to crash when it tries to access a `null` property, leading to a poor user experience.  
* **Mitigation Strategy:**  
  1. **Robust Data Models:** The `fromJson` factory constructors in our Dart models (`Order.fromJson`, `MenuItem.fromJson`) will be written defensively. They will provide default values for any non-critical missing fields.  
  2. **Error Handling on Parse:** For critical missing fields (like an order's `totalPrice`), the `fromJson` constructor will throw a specific, catchable exception. The UI layer will catch this exception and gracefully handle it, for example, by hiding the corrupt item from a list and logging the error for debugging.  
* **Test Case:** Manually edit a document in the Firebase Console to remove a required field. Reload the app and observe that it does not crash and handles the missing data gracefully.

### **2.5. RISK-05: Splash Screen Hang**

* **Pattern:** The user launches the app, but due to a very poor network connection or a temporary Firebase outage, the initial `firebase_core` initialization or `authStateChanges` check never completes.  
* **Impact:** High. The user is stuck on the splash screen indefinitely, making the app completely unusable.  
* **Mitigation Strategy:**  
  1. **Initialization Timeout:** The splash screen logic will include a timeout mechanism (e.g., `Future.delayed(Duration(seconds: 10))`).  
  2. **Fail-over Navigation:** If the Firebase initialization and auth check do not complete before the timeout, the app will automatically navigate to a dedicated "Connection Error" screen or, more simply, to the **Login Screen**.  
  3. **Error Messaging:** The Login Screen will display a subtle, non-blocking message at the top, like "Could not connect to services. Please check your internet connection."  
* **Test Case:** Launch the app with the device in airplane mode. Verify that after 10 seconds, the app navigates away from the splash screen to the login page.

