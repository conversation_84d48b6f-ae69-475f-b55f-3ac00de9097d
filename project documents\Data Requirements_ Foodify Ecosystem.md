# **Data Requirements: Foodify Ecosystem**

* **Document Version:** 1.0  
* **Date:** August 2, 2025  
* **Core Purpose:** To define the structure, validation rules, sources, and flow of all data within the Foodify system, ensuring data integrity and consistency across all client applications (Web Admin Panel and Flutter App).

## **1\. Primary Data Source**

There is only one source of truth for all application data:

* **Source:** Google Cloud Firestore  
* **Architecture:** All clients communicate directly with the Firestore database using the Firebase SDK. There is no intermediary backend server.

## **2\. High-Level Data Flow**

The system operates on a real-time, reactive data flow model. Clients write data directly to Firestore, and Firestore pushes updates to all subscribed clients simultaneously.

graph TD  
    subgraph "Client Applications (Flutter App & Web Admin)"  
        A\[User Input \<br\> e.g., Tapping 'Charge' on POS\] \--\> B(Data Transformation \<br\> Dart/JS Object to JSON Map);  
        B \--\> C{Write to Firestore \<br\> e.g., \`addDoc()\`};  
    end

    subgraph "Cloud Firestore (Single Source of Truth)"  
        D\[Database Collections \<br\> e.g., /orders\]  
    end

    subgraph "Real-time Data Display"  
        E(Real-time Listener \<br\> e.g., \`snapshots()\`) \--\> F(Data Transformation \<br\> JSON Map to Dart/JS Object);  
        F \--\> G\[Live UI Update \<br\> e.g., Dashboard refreshes\];  
    end

    C \--\> D;  
    D \--\> E;

## **3\. Data Schemas & Validation Rules**

These are the strict contracts for our Firestore collections.

### **3.1. `menu` Collection**

* **Description:** Stores all individual menu items.  
* **Schema:**

| Field | Data Type | Validation Rules | Example |
| :---- | :---- | :---- | :---- |
| `name` | `string` | Required, non-empty. | `"Shawarma Lahma"` |
| `description` | `string` | Optional. | `"Premium beef shawarma..."` |
| `price` | `number` | Required, must be \>= 0\. | `75.50` |
| `category` | `string` | Required, non-empty. | `"Main Courses"` |
| `imageUrl` | `string` | Optional, must be a valid URL format. | `"https://.../image.jpg"` |
| `isAvailable` | `boolean` | Required. `true` or `false`. | `true` |

### **3.2. `orders` Collection**

* **Description:** Stores all customer orders.  
* **Schema:**

| Field | Data Type | Validation Rules | Example |
| :---- | :---- | :---- | :---- |
| `customerName` | `string` | Required, non-empty. | `"Adham Nour"` |
| `totalPrice` | `number` | Required, must be \> 0\. | `165.00` |
| `status` | `string` | Required. Must be one of: `"pending"`, `"preparing"`, `"ready"`, `"completed"`, `"rejected"`. | `"pending"` |
| `createdAt` | `timestamp` | Required. Must be a valid Firestore timestamp. | `August 2, 2025 at...` |
| `items` | `array` | Required. Must contain at least one map object. | `[ { "id": "...", ... } ]` |
| `items.id` | `string` | Required. Must correspond to a document ID in the `menu` collection. | `"abc123xyz"` |
| `items.name` | `string` | Required. | `"Shawarma Lahma"` |
| `items.quantity` | `number` | Required, must be an integer \> 0\. | `2` |
| `items.price` | `number` | Required, must be \>= 0\. | `75.50` |

## **4\. Data Transformation**

The primary data transformation occurs on the client side.

* **Read Operations (Firestore \-\> App):** When data is read from Firestore, it arrives as a JSON-like `Map<String, dynamic>`. This map is immediately passed to a factory constructor (e.g., `MenuItem.fromJson(data)`) which transforms the raw map into a strongly-typed Dart object. This prevents runtime errors and ensures data is used safely throughout the app.  
* **Write Operations (App \-\> Firestore):** Before writing data to Firestore, the strongly-typed Dart object is transformed back into a `Map<String, dynamic>` using a `toJson()` method. This ensures the data conforms to the schema defined above.

## **5\. Data Flow for App Initialization (Splash Screen)**

The splash screen's behavior is determined by the initial flow of authentication data, not Firestore data.

1. **Source:** Firebase Authentication SDK.  
2. **Data Request:** On app launch, the client requests the current authentication state (`authStateChanges()` stream).  
3. **Data Received:** The SDK returns one of two states: `null` (no user is signed in) or a `User` object (a user is signed in).  
4. **Flow Control:**  
   * If `null` is received, the app navigates to the **Login Screen**.  
   * If a `User` object is received, the app navigates to the **Dashboard Screen**.  
5. **Splash Screen Duration:** The splash screen is displayed for the entire duration of this data request and flow control process, providing a seamless transition for the user.

