\# Coding Standards Guide: Foodify App

\* \*\*Document Version:\*\* 1.0  
\* \*\*Date:\*\* August 2, 2025  
\* \*\*Core Purpose:\*\* To establish a clear, consistent, and enforceable set of coding standards for the Foodify Flutter project. This guide is the single source of truth for code style, structure, and best practices.

\---

\#\# 1\. General Principles

1\.  \*\*Clarity Over Cleverness:\*\* Code should be simple, readable, and easy to understand. Avoid overly complex one-liners or obscure language features if a more straightforward approach exists.  
2\.  \*\*Consistency is Key:\*\* The codebase should look like it was written by a single person. Adhere strictly to the conventions laid out in this document.  
3\.  \*\*Don't Repeat Yourself (DRY):\*\* Abstract reusable logic and widgets into their own functions and classes.  
4\.  \*\*Embrace Immutability:\*\* Prefer immutable state and objects wherever possible. Use \`final\` for variables that are not reassigned.

\---

\#\# 2\. File & Directory Structure

The project MUST adhere to the following structure within the \`/lib\` directory. This pattern separates concerns and makes the codebase predictable.

\* \*\*/lib\*\*  
    \* \*\*/common:\*\* Shared widgets, constants, and utilities used across multiple features.  
        \* \`/widgets\`: Reusable widgets (e.g., \`PrimaryButton\`, \`StatCard\`).  
        \* \`/constants\`: App-wide constants (e.g., color values, padding sizes).  
    \* \*\*/features:\*\* Contains distinct application features. Each feature has its own screens, widgets, and providers.  
        \* \*\*/auth\*\*  
        \* \*\*/dashboard\*\*  
        \* \*\*/pos\*\*  
        \* ...  
    \* \*\*/models:\*\* Contains the Dart data classes that model our Firestore documents (e.g., \`menu\_item.dart\`, \`order.dart\`). These classes MUST include \`fromJson\` and \`toJson\` methods.  
    \* \*\*/services:\*\* Contains the repository classes responsible for all communication with external services (i.e., Firebase).  
        \* \`auth\_repository.dart\`  
        \* \`firestore\_repository.dart\`  
    \* \`main.dart\`: The application entry point.

\---

\#\# 3\. Naming Conventions

\* \*\*Files:\*\* Use \`snake\_case.dart\` (e.g., \`pos\_screen.dart\`, \`auth\_repository.dart\`).  
\* \*\*Classes:\*\* Use \`PascalCase\` (e.g., \`PosScreen\`, \`AuthRepository\`).  
\* \*\*Methods & Variables:\*\* Use \`camelCase\` (e.g., \`signInWithEmail\`, \`totalPrice\`).  
\* \*\*Constants:\*\* Use \`camelCase\` (e.g., \`defaultPadding\`, \`primaryColor\`).  
\* \*\*Providers (Riverpod):\*\* Use \`camelCase\` followed by \`Provider\` (e.g., \`authRepositoryProvider\`, \`menuItemsStreamProvider\`).

\---

\#\# 4\. Linting

\* \*\*Rule Set:\*\* The project MUST use the \`flutter\_lints\` package with its default recommended rules.  
\* \*\*Zero Warning Policy:\*\* All code submitted must be free of any warnings generated by the linter. This is non-negotiable and will be enforced.

\---

\#\# 5\. State Management (Riverpod)

Riverpod is our sole state management solution. Follow these patterns strictly.

\* \*\*Repositories:\*\* Repositories that communicate with Firebase should be exposed via a simple \`Provider\`.

    \* \*\*Good Example:\*\*  
        \`\`\`dart  
        // In auth\_repository.dart  
        class AuthRepository { ... }

        // In a providers file  
        final authRepositoryProvider \= Provider\<AuthRepository\>((ref) {  
          return AuthRepository();  
        });  
        \`\`\`

\* \*\*UI State:\*\* State that is specific to a screen or feature should be managed by a \`StateNotifierProvider\`.

    \* \*\*Good Example:\*\*  
        \`\`\`dart  
        // In pos\_screen\_notifier.dart  
        class PosScreenState { ... } // Immutable state class

        class PosScreenNotifier extends StateNotifier\<PosScreenState\> { ... }

        // In a providers file  
        final posScreenProvider \= StateNotifierProvider\<PosScreenNotifier, PosScreenState\>((ref) {  
          return PosScreenNotifier();  
        });  
        \`\`\`

\* \*\*Fetching Data:\*\* Use \`FutureProvider\` for one-time data fetches and \`StreamProvider\` for real-time data streams.

    \* \*\*Good Example:\*\*  
        \`\`\`dart  
        // Provider for a real-time stream of orders  
        final activeOrdersStreamProvider \= StreamProvider\<List\<Order\>\>((ref) {  
          final firestoreRepo \= ref.watch(firestoreRepositoryProvider);  
          return firestoreRepo.watchActiveOrders();  
        });  
        \`\`\`

\* \*\*Anti-Pattern:\*\* Do not create providers inside the \`build\` method of a widget. Always define them globally as \`final\` variables.

\---

\#\# 6\. UI & Widgets

\* \*\*Use \`const\` Constructors:\*\* Always use \`const\` for constructors of stateless and stateful widgets where possible. This improves performance by preventing unnecessary widget rebuilds.  
\* \*\*Split Large Widgets:\*\* If a \`build\` method becomes too long or deeply nested, refactor parts of it into smaller, private widgets. This improves readability and performance.  
\* \*\*Build Method Purity:\*\* The \`build\` method should be free of side effects. Any logic that fetches data or performs calculations should be handled within a Riverpod provider.

\---

\#\# 7\. Security Patterns & Anti-Patterns

Given our reliance on a client-to-backend model with Firebase, security is paramount.

\* \*\*Pattern: Centralize Security Rules\*\*  
    \* \*\*Good:\*\* All access control logic MUST be defined in the \`firestore.rules\` file on the Firebase console. The app should assume that if a read/write operation succeeds, it was permitted.  
    \* \*\*Anti-Pattern:\*\* Never implement client-side logic that tries to decide if a user "should" have access to something (e.g., \`if (user.role \== 'admin') { ... }\`). This is insecure and easily bypassed.

\* \*\*Pattern: Handle Authentication State Reactively\*\*  
    \* \*\*Good:\*\* Use the \`authStateChanges()\` stream to reactively navigate the user or change the UI based on their login state. This ensures the app always reflects the user's true authentication status.  
    \* \*\*Anti-Pattern:\*\* Storing the user's login status in a simple boolean variable (\`isLoggedIn \= true\`). This can become out of sync if the user's session is revoked on the backend.

\* \*\*Pattern: No Sensitive Information on the Client\*\*  
    \* \*\*Anti-Pattern:\*\* Never hardcode API keys, credentials, or any other sensitive strings directly in the Dart code. Use environment variables via \`.env\` files for configuration.

